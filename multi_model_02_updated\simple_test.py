#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的测试脚本，验证核心功能
"""

import sys
from pathlib import Path
from unittest.mock import Mock, patch
import tempfile

# 添加代码路径
sys.path.insert(0, str(Path(__file__).parent / 'code'))

def test_timer_manager():
    """测试TimerManager类"""
    print("测试TimerManager类...")
    
    # 导入TimerManager
    try:
        from gui_main import TimerManager
        print("✅ TimerManager导入成功")
    except ImportError:
        print("❌ TimerManager导入失败")
        return False
    
    # 创建模拟的root
    mock_root = Mock()
    mock_root.after.return_value = 'timer_id'
    
    # 创建TimerManager实例
    timer_manager = TimerManager(mock_root)
    
    # 测试设置定时器
    callback = Mock()
    timer_id = timer_manager.set_timer('test', 1000, callback)
    
    assert timer_id == 'timer_id', f"期望'timer_id'，得到{timer_id}"
    assert 'test' in timer_manager.timers, "定时器未添加到timers字典"
    print("✅ 设置定时器测试通过")
    
    # 测试取消定时器
    timer_manager.cancel_timer('test')
    assert 'test' not in timer_manager.timers, "定时器未从timers字典中移除"
    mock_root.after_cancel.assert_called_with('timer_id')
    print("✅ 取消定时器测试通过")
    
    # 测试取消所有定时器
    timer_manager.set_timer('test1', 1000, Mock())
    timer_manager.set_timer('test2', 1000, Mock())
    timer_manager.cancel_all()
    assert len(timer_manager.timers) == 0, "所有定时器未被取消"
    print("✅ 取消所有定时器测试通过")
    
    return True

def test_gui_creation():
    """测试GUI创建（不实际创建窗口）"""
    print("\n测试GUI创建...")
    
    # 模拟tkinter
    with patch('tkinter.Tk') as mock_tk, \
         patch('tkinter.StringVar') as mock_string_var, \
         patch('tkinter.BooleanVar') as mock_bool_var, \
         patch('tkinter.DoubleVar') as mock_double_var, \
         patch('tkinter.ttk.Notebook') as mock_notebook, \
         patch('tkinter.ttk.Frame') as mock_frame, \
         patch('tkinter.Canvas') as mock_canvas, \
         patch('tkinter.Scrollbar') as mock_scrollbar, \
         patch('tkinter.ttk.Label') as mock_label, \
         patch('tkinter.ttk.Button') as mock_button, \
         patch('tkinter.Text') as mock_text, \
         patch('tkinter.ttk.Progressbar') as mock_progress, \
         patch('tkinter.ttk.Treeview') as mock_treeview, \
         patch('tkinter.ttk.Combobox') as mock_combobox, \
         patch('tkinter.Checkbutton') as mock_checkbutton, \
         patch('tkinter.Radiobutton') as mock_radiobutton, \
         patch('tkinter.ttk.Separator') as mock_separator, \
         patch('tkinter.ttk.Scale') as mock_scale, \
         patch('tkinter.Listbox') as mock_listbox, \
         patch('tkinter.Menu') as mock_menu, \
         patch('matplotlib.backends.backend_tkagg.FigureCanvasTkAgg') as mock_canvas_agg, \
         patch('matplotlib.backends.backend_tkagg.NavigationToolbar2Tk') as mock_toolbar, \
         patch('matplotlib.figure.Figure') as mock_figure, \
         patch('plt.Figure') as mock_plt_figure, \
         patch('matplotlib.pyplot.figure') as mock_plt_figure_func:
        
        # 设置模拟返回值
        mock_root = Mock()
        mock_tk.return_value = mock_root
        mock_string_var.return_value = Mock()
        mock_bool_var.return_value = Mock()
        mock_double_var.return_value = Mock()
        
        # 模拟必要的模块
        sys.modules['config'] = Mock()
        sys.modules['config'].MODEL_NAMES = ['RandomForest', 'XGBoost', 'Logistic']
        sys.modules['config'].MODEL_DISPLAY_NAMES = {
            'RandomForest': '随机森林',
            'XGBoost': 'XGBoost',
            'Logistic': '逻辑回归'
        }
        sys.modules['config'].OUTPUT_PATH = Path(tempfile.mkdtemp()) / 'output'
        sys.modules['config'].CACHE_PATH = Path(tempfile.mkdtemp()) / 'cache'
        sys.modules['config'].RANDOM_SEED = 42
        sys.modules['config'].set_global_seed = Mock()
        sys.modules['config'].REPRODUCIBILITY_CONFIG = {}
        sys.modules['config'].apply_reproducibility_env = Mock()
        sys.modules['config'].OPTIMIZED_GPU_CONFIG = {'use_gpu': False}
        
        sys.modules['logger'] = Mock()
        sys.modules['logger'].get_logger = Mock(return_value=Mock())
        
        # 尝试导入和创建GUI
        try:
            from gui_main import MLPlatformGUI
            gui = MLPlatformGUI()
            print("✅ GUI创建成功")
            
            # 验证关键属性
            assert hasattr(gui, 'timer_manager'), "缺少timer_manager属性"
            assert hasattr(gui, 'model_vars'), "缺少model_vars属性"
            assert len(gui.model_vars) == 3, f"期望3个模型变量，得到{len(gui.model_vars)}个"
            print("✅ GUI属性验证通过")
            
            return True
            
        except Exception as e:
            print(f"❌ GUI创建失败: {e}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """运行所有测试"""
    print("开始运行简化的测试...")
    print("=" * 50)
    
    results = []
    
    # 测试TimerManager
    results.append(test_timer_manager())
    
    # 测试GUI创建
    results.append(test_gui_creation())
    
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    print(f"通过: {sum(results)}/{len(results)}")
    
    if all(results):
        print("🎉 所有测试通过！")
        return 0
    else:
        print("❌ 部分测试失败")
        return 1

if __name__ == '__main__':
    sys.exit(main())