#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析缓存结果中的AUC=1.0问题
"""

import numpy as np
import pandas as pd
from pathlib import Path
from joblib import load
from sklearn.metrics import roc_auc_score, accuracy_score, confusion_matrix
import json
import os

# 添加代码路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import CACHE_PATH

def analyze_cached_results():
    """
    分析所有模型的缓存结果，检查AUC=1.0的问题
    """
    print("=" * 80)
    print("分析模型缓存结果中的AUC=1.0问题")
    print("=" * 80)
    
    # 10个模型列表
    model_names = ['DecisionTree', 'RandomForest', 'XGBoost', 'LightGBM', 'CatBoost',
                   'Logistic', 'SVM', 'NeuralNet', 'NaiveBayes', 'KNN']
    
    models_with_auc_1 = []
    analysis_results = {}
    
    for model_name in model_names:
        print(f"\n分析模型: {model_name}")
        print("-" * 50)
        
        cache_file = CACHE_PATH / f"{model_name}_results.joblib"
        
        if not cache_file.exists():
            print(f"缓存文件不存在: {cache_file}")
            continue
        
        try:
            # 加载缓存结果
            result = load(cache_file)
            
            # 获取数据
            y_true = result['y_true']
            y_pred = result['y_pred']
            y_score = result.get('y_score', None)
            
            # 计算指标
            accuracy = accuracy_score(y_true, y_pred)
            
            print(f"测试集大小: {len(y_true)}")
            print(f"预测正确数: {(y_true == y_pred).sum()}")
            print(f"准确率: {accuracy:.4f}")
            
            if y_score is not None:
                # 确保y_score是一维数组
                if hasattr(y_score, 'ravel'):
                    y_score = y_score.ravel()
                
                # 检查y_score的值分布
                print(f"y_score最小值: {np.min(y_score):.6f}")
                print(f"y_score最大值: {np.max(y_score):.6f}")
                print(f"y_score唯一值数量: {len(np.unique(y_score))}")
                
                # 计算AUC
                auc = roc_auc_score(y_true, y_score)
                print(f"AUC: {auc:.6f}")
                
                # 分析结果
                analysis_results[model_name] = {
                    'accuracy': accuracy,
                    'auc': auc,
                    'y_score_min': float(np.min(y_score)),
                    'y_score_max': float(np.max(y_score)),
                    'y_score_unique': len(np.unique(y_score)),
                    'correct_predictions': int((y_true == y_pred).sum()),
                    'total_samples': len(y_true)
                }
                
                # 如果AUC为1.0，进行详细分析
                if auc == 1.0:
                    models_with_auc_1.append(model_name)
                    print("*** 警告: AUC = 1.0 ***")
                    
                    # 分析预测结果
                    error_indices = np.where(y_true != y_pred)[0]
                    print(f"预测错误的位置: {error_indices}")
                    
                    # 检查混淆矩阵
                    cm = confusion_matrix(y_true, y_pred)
                    print(f"混淆矩阵:\n{cm}")
                    
                    # 检查y_score的分离度
                    if len(np.unique(y_score)) >= 2:
                        pos_scores = y_score[y_true == 1]
                        neg_scores = y_score[y_true == 0]
                        print(f"正类分数范围: [{np.min(pos_scores):.6f}, {np.max(pos_scores):.6f}]")
                        print(f"负类分数范围: [{np.min(neg_scores):.6f}, {np.max(neg_scores):.6f}]")
                        
                        # 检查是否有完全分离
                        min_pos = np.min(pos_scores)
                        max_neg = np.max(neg_scores)
                        if min_pos > max_neg:
                            print(f"*** 完全分离! 正类最小分数({min_pos:.6f}) > 负类最大分数({max_neg:.6f}) ***")
                    else:
                        print("*** y_score只有一个唯一值 ***")
            else:
                print("警告: 无法获取y_score")
                analysis_results[model_name] = {
                    'accuracy': accuracy,
                    'auc': None,
                    'y_score_min': None,
                    'y_score_max': None,
                    'y_score_unique': 0,
                    'correct_predictions': int((y_true == y_pred).sum()),
                    'total_samples': len(y_true)
                }
                
        except Exception as e:
            print(f"分析失败: {e}")
            analysis_results[model_name] = {'error': str(e)}
    
    # 保存分析结果
    with open(CACHE_PATH / 'auc_analysis_results.json', 'w', encoding='utf-8') as f:
        json.dump(analysis_results, f, ensure_ascii=False, indent=2)
    
    # 总结
    print("\n" + "=" * 80)
    print("分析总结")
    print("=" * 80)
    
    if models_with_auc_1:
        print(f"\n发现AUC=1.0的模型: {', '.join(models_with_auc_1)}")
        print(f"\n这些模型占总模型数的: {len(models_with_auc_1)}/{len(model_names)} ({len(models_with_auc_1)/len(model_names)*100:.1f}%)")
        
        print("\n可能的原因:")
        print("1. 数据过于简单，特征可以完美分离类别")
        print("2. 数据泄露（测试集信息出现在训练集中）")
        print("3. 模型过拟合（尤其是深度较大的树模型）")
        print("4. 数据集太小，模型记住了所有样本")
        print("5. 标签分布不平衡导致模型倾向于预测多数类")
        
        # 建议检查数据
        print("\n建议检查:")
        print("1. 检查特征和目标列是否有强相关性")
        print("2. 检查数据预处理是否有泄露")
        print("3. 考虑使用交叉验证验证结果")
        print("4. 尝试简化模型或增加正则化")
        print("5. 检查数据集大小是否足够")
        
    else:
        print("\n未发现AUC=1.0的模型")
    
    return analysis_results

if __name__ == "__main__":
    analyze_cached_results()