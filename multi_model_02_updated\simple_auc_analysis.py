#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接分析缓存结果
"""

import sys
import os
import numpy as np
from pathlib import Path
from joblib import load

# 设置路径
cache_path = Path(r"D:\Code\MM01U\multi_model_02_updated\cache")

# 模型列表
model_names = ['DecisionTree', 'RandomForest', 'XGBoost', 'LightGBM', 'CatBoost',
               'Logistic', 'SVM', 'NeuralNet', 'NaiveBayes', 'KNN']

print("分析模型缓存结果...")
print("=" * 60)

for model_name in model_names:
    cache_file = cache_path / f"{model_name}_results.joblib"
    
    if cache_file.exists():
        try:
            result = load(cache_file)
            y_true = result['y_true']
            y_pred = result['y_pred']
            y_score = result.get('y_score', None)
            
            # 计算准确率
            accuracy = np.mean(y_true == y_pred)
            
            print(f"\n{model_name}:")
            print(f"  样本数: {len(y_true)}")
            print(f"  准确率: {accuracy:.4f}")
            
            if y_score is not None:
                # 确保y_score是一维
                if hasattr(y_score, 'ravel'):
                    y_score = y_score.ravel()
                
                # 计算AUC
                from sklearn.metrics import roc_auc_score
                auc = roc_auc_score(y_true, y_score)
                print(f"  AUC: {auc:.6f}")
                
                if auc == 1.0:
                    print(f"  *** AUC=1.0! ***")
                    
                    # 检查预测分数分布
                    unique_scores = len(np.unique(y_score))
                    print(f"  唯一分数值: {unique_scores}")
                    
                    if unique_scores == 1:
                        print(f"  警告: 所有样本预测分数相同 ({y_score[0]:.6f})")
                    else:
                        pos_scores = y_score[y_true == 1]
                        neg_scores = y_score[y_true == 0]
                        if len(pos_scores) > 0 and len(neg_scores) > 0:
                            min_pos = np.min(pos_scores)
                            max_neg = np.max(neg_scores)
                            if min_pos > max_neg:
                                print(f"  完全分离: 正类最小({min_pos:.4f}) > 负类最大({max_neg:.4f})")
            else:
                print(f"  无预测分数")
                
        except Exception as e:
            print(f"\n{model_name}: 加载失败 - {e}")
    else:
        print(f"\n{model_name}: 缓存文件不存在")

print("\n" + "=" * 60)