import os
import sys
import numpy as np
from pathlib import Path

# 手动实现AUC计算，避免sklearn依赖问题
def manual_auc_calculation(y_true, y_score):
    """手动计算AUC"""
    # 确保输入是numpy数组
    y_true = np.array(y_true)
    y_score = np.array(y_score)
    
    # 只处理二分类
    unique_classes = np.unique(y_true)
    if len(unique_classes) != 2:
        return None
    
    # 确保标签是0和1
    if set(unique_classes) == {0, 1}:
        pass
    else:
        # 映射标签
        y_true_mapped = np.zeros_like(y_true)
        y_true_mapped[y_true == unique_classes[1]] = 1
        y_true = y_true_mapped
    
    # 计算AUC
    n_pos = np.sum(y_true == 1)
    n_neg = np.sum(y_true == 0)
    
    if n_pos == 0 or n_neg == 0:
        return None
    
    # 排序
    indices = np.argsort(y_score)[::-1]
    y_true_sorted = y_true[indices]
    
    # 计算累积的正类数量
    cum_pos = np.cumsum(y_true_sorted)
    
    # 计算TPR和FPR
    tpr = cum_pos / n_pos
    fpr = (np.arange(1, len(y_true) + 1) - cum_pos) / n_neg
    
    # 在开头添加(0,0)
    tpr = np.concatenate([[0], tpr])
    fpr = np.concatenate([[0], fpr])
    
    # 计算AUC（梯形法则）
    auc = np.trapz(tpr, fpr)
    
    return auc

# 主分析函数
def analyze_models():
    cache_path = Path(r"D:\Code\MM01U\multi_model_02_updated\cache")
    model_names = ['DecisionTree', 'RandomForest', 'XGBoost', 'LightGBM', 'CatBoost',
                   'Logistic', 'SVM', 'NeuralNet', 'NaiveBayes', 'KNN']
    
    print("手动分析模型AUC值")
    print("=" * 50)
    
    for model_name in model_names:
        cache_file = cache_path / f"{model_name}_results.joblib"
        
        if cache_file.exists():
            try:
                # 读取numpy数组文件
                import pickle
                with open(cache_file, 'rb') as f:
                    result = pickle.load(f)
                
                y_true = result['y_true']
                y_pred = result['y_pred']
                y_score = result.get('y_score', None)
                
                # 计算准确率
                accuracy = np.mean(y_true == y_pred)
                
                print(f"\n{model_name}:")
                print(f"  准确率: {accuracy:.4f}")
                
                if y_score is not None:
                    # 处理y_score
                    if hasattr(y_score, 'ravel'):
                        y_score = y_score.ravel()
                    
                    # 手动计算AUC
                    auc = manual_auc_calculation(y_true, y_score)
                    if auc is not None:
                        print(f"  AUC: {auc:.6f}")
                        
                        if auc == 1.0:
                            print(f"  *** AUC=1.0! ***")
                    else:
                        print(f"  AUC: 无法计算")
                else:
                    print(f"  无y_score数据")
                    
            except Exception as e:
                print(f"\n{model_name}: 错误 - {e}")
        else:
            print(f"\n{model_name}: 文件不存在")

if __name__ == "__main__":
    analyze_models()