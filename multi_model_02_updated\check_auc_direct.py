#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接检查缓存文件中的AUC值
"""

import sys
sys.path.append('D:/anaconda/envs/multi_model/Lib/site-packages')

import numpy as np
from joblib import load
from pathlib import Path
from sklearn.metrics import roc_auc_score
import os

# 设置路径
cache_path = Path(r"D:\Code\MM01U\multi_model_02_updated\cache")

# 模型列表
model_names = ['DecisionTree', 'RandomForest', 'XGBoost', 'LightGBM', 'CatBoost',
               'Logistic', 'SVM', 'NeuralNet', 'NaiveBayes', 'KNN']

print("检查各模型的AUC值")
print("=" * 60)

auc_issues = []

for model_name in model_names:
    cache_file = cache_path / f"{model_name}_results.joblib"
    
    if cache_file.exists():
        try:
            # 加载结果
            result = load(cache_file)
            
            # 获取数据
            y_true = result['y_true']
            y_pred = result['y_pred']
            y_score = result.get('y_score', None)
            
            # 计算准确率
            accuracy = np.mean(y_true == y_pred)
            
            print(f"\n模型: {model_name}")
            print(f"样本数: {len(y_true)}")
            print(f"准确率: {accuracy:.4f}")
            
            # 检查AUC
            if y_score is not None:
                # 处理y_score的形状
                if hasattr(y_score, 'ravel'):
                    y_score_flat = y_score.ravel()
                elif hasattr(y_score, 'flatten'):
                    y_score_flat = y_score.flatten()
                else:
                    y_score_flat = y_score
                
                # 确保是二分类问题
                unique_classes = np.unique(y_true)
                if len(unique_classes) == 2:
                    # 计算AUC
                    auc = roc_auc_score(y_true, y_score_flat)
                    print(f"AUC: {auc:.6f}")
                    
                    # 检查是否为1.0
                    if auc == 1.0:
                        auc_issues.append(model_name)
                        print("*** 问题：AUC = 1.0 ***")
                        
                        # 详细分析
                        print("详细分析:")
                        print(f"- y_score范围: [{np.min(y_score_flat):.6f}, {np.max(y_score_flat):.6f}]")
                        print(f"- y_score唯一值数: {len(np.unique(y_score_flat))}")
                        
                        # 检查是否完全分离
                        if len(np.unique(y_score_flat)) > 1:
                            pos_scores = y_score_flat[y_true == 1]
                            neg_scores = y_score_flat[y_true == 0]
                            if len(pos_scores) > 0 and len(neg_scores) > 0:
                                min_pos = np.min(pos_scores)
                                max_neg = np.max(neg_scores)
                                if min_pos > max_neg:
                                    print(f"- 完全分离：正类最小值({min_pos:.6f}) > 负类最大值({max_neg:.6f})")
                        
                        # 检查预测是否全部正确
                        if accuracy == 1.0:
                            print("- 所有预测都正确")
                else:
                    print(f"非二分类问题（{len(unique_classes)}个类别），跳过AUC计算")
            else:
                print("无y_score数据")
                
        except Exception as e:
            print(f"处理 {model_name} 时出错: {e}")
    else:
        print(f"\n模型 {model_name} 的缓存文件不存在")

# 总结
print("\n" + "=" * 60)
print("总结报告")
print("=" * 60)

if auc_issues:
    print(f"\n发现AUC=1.0的模型数量: {len(auc_issues)}")
    print("这些模型是:")
    for model in auc_issues:
        print(f"  - {model}")
    
    print("\n可能的原因:")
    print("1. 数据集过于简单，模型可以完美分类")
    print("2. 数据泄露：测试集信息出现在训练集中")
    print("3. 模型过拟合：特别是树模型深度过大")
    print("4. 数据集太小：模型记住了所有样本")
    
    print("\n建议解决方案:")
    print("1. 检查数据预处理流程，确保没有数据泄露")
    print("2. 使用交叉验证而不是单次train/test分割")
    print("3. 增加正则化或简化模型复杂度")
    print("4. 收集更多数据以避免过拟合")
else:
    print("\n未发现AUC=1.0的模型")
    print("所有模型的AUC计算正常")