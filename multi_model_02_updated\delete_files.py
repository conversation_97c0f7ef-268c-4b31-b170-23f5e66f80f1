import os
import shutil

# Files to delete
files_to_delete = [
    'check_auc_direct.py',
    'analyze_cached_auc.py', 
    'simple_auc_analysis.py',
    'quick_auc_check.py',
    'manual_auc_check.py',
    'parse_auc_report.py',
    'auc_analysis_report.md'
]

# Files in code directory
code_files_to_delete = [
    'code/check_auc_issue.py',
    'code/analyze_cached_auc.py'
]

print("Deleting test files...")

# Delete main directory files
for file in files_to_delete:
    try:
        if os.path.exists(file):
            os.remove(file)
            print(f"Deleted: {file}")
        else:
            print(f"Not found: {file}")
    except Exception as e:
        print(f"Error deleting {file}: {e}")

# Delete code directory files
for file in code_files_to_delete:
    try:
        if os.path.exists(file):
            os.remove(file)
            print(f"Deleted: {file}")
        else:
            print(f"Not found: {file}")
    except Exception as e:
        print(f"Error deleting {file}: {e}")

print("\nDeletion complete!")