import json

# 读取性能报告
with open(r'D:\Code\MM01U\multi_model_02_updated\reports\performance_data.json', 'r', encoding='utf-8') as f:
    data = json.load(f)

print("模型AUC值分析报告")
print("=" * 50)

auc_1_models = []

for model_name, metrics in data['detailed_metrics'].items():
    auc = metrics.get('auc_roc', None)
    accuracy = metrics.get('accuracy', None)
    
    if auc is not None:
        print(f"{model_name:12s} | 准确率: {accuracy:.3f} | AUC: {auc:.6f}")
        
        if auc == 1.0:
            auc_1_models.append(model_name)
    else:
        print(f"{model_name:12s} | 准确率: {accuracy:.3f} | AUC: N/A")

print("\n" + "=" * 50)
print("总结:")
print(f"AUC = 1.0的模型数量: {len(auc_1_models)}")

if auc_1_models:
    print("\nAUC=1.0的模型:")
    for model in auc_1_models:
        print(f"  - {model}")
    
    print("\n这些模型的详细指标:")
    for model in auc_1_models:
        metrics = data['detailed_metrics'][model]
        print(f"\n{model}:")
        print(f"  准确率: {metrics['accuracy']:.3f}")
        print(f"  精确率: {metrics['precision']:.3f}")
        print(f"  召回率: {metrics['recall']:.3f}")
        print(f"  F1分数: {metrics['f1_score']:.3f}")
        print(f"  特异性: {metrics['specificity']:.3f}")
        print(f"  AUC-ROC: {metrics['auc_roc']:.6f}")
        print(f"  AUC-PR: {metrics['auc_pr']:.6f}")
else:
    print("\n未发现AUC=1.0的模型")
    print("所有模型的AUC值都在正常范围内")