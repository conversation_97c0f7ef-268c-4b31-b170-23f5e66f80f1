#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查模型AUC为1.0的问题
"""

import numpy as np
import pandas as pd
from pathlib import Path
from joblib import load
from sklearn.metrics import roc_auc_score, accuracy_score, confusion_matrix
import sys
import os

# 添加代码路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from model_training import MODEL_TRAINERS
from data_preprocessing import load_and_preprocess_data
from config import CACHE_PATH

def check_model_auc_issue(data_path):
    """
    检查各模型AUC计算问题
    
    Args:
        data_path: 数据文件路径
    """
    print("=" * 80)
    print("检查模型AUC为1.0的问题")
    print("=" * 80)
    
    # 1. 加载和预处理数据
    print("\n1. 数据加载和预处理")
    print("-" * 40)
    X_train, X_test, y_train, y_test = load_and_preprocess_data(data_path)
    
    print(f"训练集大小: {X_train.shape}")
    print(f"测试集大小: {X_test.shape}")
    print(f"训练集类别分布: {np.bincount(y_train)}")
    print(f"测试集类别分布: {np.bincount(y_test)}")
    
    # 2. 检查每个模型
    print("\n2. 各模型AUC检查")
    print("-" * 40)
    
    models_with_auc_1 = []
    
    for model_name, trainer in MODEL_TRAINERS.items():
        print(f"\n检查模型: {model_name}")
        print("-" * 30)
        
        try:
            # 训练模型
            model = trainer.train_and_evaluate(
                X_train, y_train, X_test, y_test, 
                params=None, use_gpu=False, n_jobs=1
            )
            
            # 加载缓存的结果
            cache_file = CACHE_PATH / f"{model_name}_results.joblib"
            if cache_file.exists():
                result = load(cache_file)
                
                # 获取预测结果
                y_true = result['y_true']
                y_pred = result['y_pred']
                y_score = result.get('y_score', None)
                
                # 计算指标
                accuracy = accuracy_score(y_true, y_pred)
                
                if y_score is not None:
                    # 确保y_score是一维数组
                    if hasattr(y_score, 'ravel'):
                        y_score = y_score.ravel()
                    
                    # 检查y_score的值分布
                    print(f"y_score的最小值: {np.min(y_score):.6f}")
                    print(f"y_score的最大值: {np.max(y_score):.6f}")
                    print(f"y_score的唯一值数量: {len(np.unique(y_score))}")
                    
                    # 计算AUC
                    auc = roc_auc_score(y_true, y_score)
                    print(f"AUC: {auc:.6f}")
                    
                    # 如果AUC为1.0，进行详细分析
                    if auc == 1.0:
                        models_with_auc_1.append(model_name)
                        print("*** 警告: AUC = 1.0 ***")
                        
                        # 分析预测结果
                        print("\n详细分析:")
                        print(f"预测正确的数量: {(y_true == y_pred).sum()}")
                        print(f"预测错误的位置: {np.where(y_true != y_pred)[0]}")
                        
                        # 检查混淆矩阵
                        cm = confusion_matrix(y_true, y_pred)
                        print(f"混淆矩阵:\n{cm}")
                        
                        # 检查y_score的分离度
                        if len(np.unique(y_score)) >= 2:
                            pos_scores = y_score[y_true == 1]
                            neg_scores = y_score[y_true == 0]
                            print(f"正类分数范围: [{np.min(pos_scores):.6f}, {np.max(pos_scores):.6f}]")
                            print(f"负类分数范围: [{np.min(neg_scores):.6f}, {np.max(neg_scores):.6f}]")
                            
                            # 检查是否有完全分离
                            min_pos = np.min(pos_scores)
                            max_neg = np.max(neg_scores)
                            if min_pos > max_neg:
                                print(f"*** 完全分离! 正类最小分数({min_pos:.6f}) > 负类最大分数({max_neg:.6f}) ***")
                else:
                    print("警告: 无法获取y_score")
                    
            else:
                print(f"警告: 找不到缓存文件 {cache_file}")
                
        except Exception as e:
            print(f"错误: {e}")
            continue
    
    # 3. 总结
    print("\n3. 问题总结")
    print("-" * 40)
    if models_with_auc_1:
        print(f"发现AUC=1.0的模型: {', '.join(models_with_auc_1)}")
        print("\n可能的原因:")
        print("1. 数据过于简单，特征可以完美分离类别")
        print("2. 数据泄露（测试集信息出现在训练集中）")
        print("3. 模型过拟合（尤其是深度较大的树模型）")
        print("4. 数据集太小，模型记住了所有样本")
        
        # 建议检查数据
        print("\n建议检查:")
        print("1. 检查特征和目标列是否有强相关性")
        print("2. 检查数据预处理是否有泄露")
        print("3. 考虑使用交叉验证验证结果")
        print("4. 尝试简化模型或增加正则化")
    else:
        print("未发现AUC=1.0的模型")

if __name__ == "__main__":
    # 使用默认数据路径
    data_path = "data/default_data.csv"
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        data_path = sys.argv[1]
    
    if not os.path.exists(data_path):
        print(f"错误: 数据文件不存在: {data_path}")
        print("请提供有效的数据文件路径")
        sys.exit(1)
    
    check_model_auc_issue(data_path)