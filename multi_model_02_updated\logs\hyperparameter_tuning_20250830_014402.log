2025-08-30 01:44:02 - hyperparameter_tuning - INFO - 开始对 DecisionTree 进行超参数调优，试验次数: 50
2025-08-30 01:44:02 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-30 01:44:02 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-30 01:44:02 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-30 01:44:02 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-30 01:44:02 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800}
2025-08-30 01:44:02 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9307
2025-08-30 01:44:02 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9385
2025-08-30 01:44:02 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9601
2025-08-30 01:44:02 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-30 01:44:02 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9601
2025-08-30 01:44:02 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-30 01:44:02 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9601
2025-08-30 01:44:02 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-30 01:44:02 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9601
2025-08-30 01:44:02 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-30 01:44:02 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9601
2025-08-30 01:44:02 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳参数: {'max_depth': 10, 'min_samples_split': 44, 'min_samples_leaf': 6, 'criterion': 'gini', 'class_weight': 'balanced', 'max_features': None}
2025-08-30 01:44:02 - hyperparameter_tuning - INFO - 模型 DecisionTree 的最佳得分: 0.9601
2025-08-30 01:44:02 - hyperparameter_tuning - INFO - 实际执行试验次数: 17/50
2025-08-30 01:44:02 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-30 01:44:02 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\DecisionTree\optimization_history_20250830_014402.html
2025-08-30 01:44:02 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\DecisionTree\param_importances_20250830_014402.html
2025-08-30 01:44:02 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.67 秒
2025-08-30 01:44:02 - hyperparameter_tuning - INFO - 开始对 RandomForest 进行超参数调优，试验次数: 50
2025-08-30 01:44:02 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-30 01:44:02 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-30 01:44:02 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-30 01:44:02 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-30 01:44:02 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 4, 'timeout': 1800}
2025-08-30 01:44:03 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9865
2025-08-30 01:44:10 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-30 01:44:10 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9867
2025-08-30 01:44:10 - hyperparameter_tuning - INFO - Trial 9: 发现更好的得分 0.9883
2025-08-30 01:44:12 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳参数: {'n_estimators': 166, 'max_depth': 25, 'min_samples_split': 15, 'min_samples_leaf': 7, 'max_features': 'log2'}
2025-08-30 01:44:12 - hyperparameter_tuning - INFO - 模型 RandomForest 的最佳得分: 0.9883
2025-08-30 01:44:12 - hyperparameter_tuning - INFO - 实际执行试验次数: 14/50
2025-08-30 01:44:12 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-30 01:44:12 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\RandomForest\optimization_history_20250830_014412.html
2025-08-30 01:44:12 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\RandomForest\param_importances_20250830_014412.html
2025-08-30 01:44:12 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 9.87 秒
2025-08-30 01:44:12 - hyperparameter_tuning - INFO - 开始对 XGBoost 进行超参数调优，试验次数: 50
2025-08-30 01:44:12 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-30 01:44:12 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-30 01:44:12 - hyperparameter_tuning - INFO - 超时设置: 300秒
2025-08-30 01:44:12 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-30 01:44:12 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 300}
2025-08-30 01:44:12 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-30 01:44:12 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9906
2025-08-30 01:44:12 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-30 01:44:12 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-30 01:44:13 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-30 01:44:13 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-30 01:44:13 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-30 01:44:13 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.9923
2025-08-30 01:44:13 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-30 01:44:14 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-30 01:44:14 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-30 01:44:14 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-30 01:44:14 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-30 01:44:15 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-30 01:44:15 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-30 01:44:15 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-30 01:44:16 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-30 01:44:16 - hyperparameter_tuning - INFO - XGBoost使用GPU加速
2025-08-30 01:44:16 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-30 01:44:16 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9924
2025-08-30 01:44:16 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳参数: {'n_estimators': 294, 'max_depth': 7, 'learning_rate': 0.10264378756176612, 'subsample': 0.5089809378074099, 'colsample_bytree': 0.5072567334400258}
2025-08-30 01:44:16 - hyperparameter_tuning - INFO - 模型 XGBoost 的最佳得分: 0.9924
2025-08-30 01:44:16 - hyperparameter_tuning - INFO - 实际执行试验次数: 16/50
2025-08-30 01:44:16 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-30 01:44:16 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\XGBoost\optimization_history_20250830_014416.html
2025-08-30 01:44:16 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\XGBoost\param_importances_20250830_014416.html
2025-08-30 01:44:16 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 4.14 秒
2025-08-30 01:44:16 - hyperparameter_tuning - INFO - 开始对 LightGBM 进行超参数调优，试验次数: 50
2025-08-30 01:44:16 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-30 01:44:16 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-30 01:44:16 - hyperparameter_tuning - INFO - 超时设置: 300秒
2025-08-30 01:44:16 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-30 01:44:16 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 300}
2025-08-30 01:44:16 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9838
2025-08-30 01:44:17 - hyperparameter_tuning - INFO - Trial 6: 发现更好的得分 0.9856
2025-08-30 01:44:18 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-30 01:44:18 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9856
2025-08-30 01:44:18 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳参数: {'n_estimators': 202, 'max_depth': 3, 'learning_rate': 0.02886496196573106, 'feature_fraction': 0.9744427686266666, 'bagging_fraction': 0.9828160165372797}
2025-08-30 01:44:18 - hyperparameter_tuning - INFO - 模型 LightGBM 的最佳得分: 0.9856
2025-08-30 01:44:18 - hyperparameter_tuning - INFO - 实际执行试验次数: 17/50
2025-08-30 01:44:18 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-30 01:44:18 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\LightGBM\optimization_history_20250830_014418.html
2025-08-30 01:44:18 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\LightGBM\param_importances_20250830_014418.html
2025-08-30 01:44:18 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 1.60 秒
2025-08-30 01:44:18 - hyperparameter_tuning - INFO - 开始对 CatBoost 进行超参数调优，试验次数: 50
2025-08-30 01:44:18 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 1
2025-08-30 01:44:18 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-30 01:44:18 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-30 01:44:18 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-30 01:44:18 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 1800}
2025-08-30 01:44:18 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-30 01:44:22 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9808
2025-08-30 01:44:22 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-30 01:44:24 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9841
2025-08-30 01:44:24 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-30 01:44:26 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-30 01:44:28 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-30 01:44:36 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9882
2025-08-30 01:44:36 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-30 01:44:41 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-30 01:44:44 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-30 01:44:49 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-30 01:44:51 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-30 01:44:55 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-30 01:45:02 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-30 01:45:05 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-30 01:45:07 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-30 01:45:09 - hyperparameter_tuning - INFO - CatBoost使用GPU加速
2025-08-30 01:45:12 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-30 01:45:12 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9882
2025-08-30 01:45:12 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳参数: {'iterations': 203, 'depth': 3, 'learning_rate': 0.09472194807521325, 'l2_leaf_reg': 4.297256589643226, 'bagging_temperature': 0.45606998421703593}
2025-08-30 01:45:12 - hyperparameter_tuning - INFO - 模型 CatBoost 的最佳得分: 0.9882
2025-08-30 01:45:12 - hyperparameter_tuning - INFO - 实际执行试验次数: 15/50
2025-08-30 01:45:12 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-30 01:45:12 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\CatBoost\optimization_history_20250830_014512.html
2025-08-30 01:45:12 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\CatBoost\param_importances_20250830_014512.html
2025-08-30 01:45:12 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 54.28 秒
2025-08-30 01:45:12 - hyperparameter_tuning - INFO - 开始对 Logistic 进行超参数调优，试验次数: 50
2025-08-30 01:45:12 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 6
2025-08-30 01:45:12 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-30 01:45:12 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-30 01:45:12 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-30 01:45:12 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 6, 'timeout': 1800}
2025-08-30 01:45:12 - hyperparameter_tuning - INFO - Trial 4: 发现更好的得分 0.9617
2025-08-30 01:45:12 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9660
2025-08-30 01:45:12 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9677
2025-08-30 01:45:13 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-30 01:45:13 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9686
2025-08-30 01:45:13 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-30 01:45:13 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9686
2025-08-30 01:45:13 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-30 01:45:13 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9686
2025-08-30 01:45:13 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-30 01:45:13 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9686
2025-08-30 01:45:13 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-30 01:45:13 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-30 01:45:13 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9686
2025-08-30 01:45:13 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9686
2025-08-30 01:45:13 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳参数: {'clf__C': 0.7982678310629142, 'clf__solver': 'liblinear'}
2025-08-30 01:45:13 - hyperparameter_tuning - INFO - 模型 Logistic 的最佳得分: 0.9686
2025-08-30 01:45:13 - hyperparameter_tuning - INFO - 实际执行试验次数: 18/50
2025-08-30 01:45:13 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-30 01:45:13 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\Logistic\optimization_history_20250830_014513.html
2025-08-30 01:45:13 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\Logistic\param_importances_20250830_014513.html
2025-08-30 01:45:13 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.94 秒
2025-08-30 01:45:13 - hyperparameter_tuning - INFO - 开始对 SVM 进行超参数调优，试验次数: 50
2025-08-30 01:45:13 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 6
2025-08-30 01:45:13 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-30 01:45:13 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-30 01:45:13 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-30 01:45:13 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 6, 'timeout': 1800}
2025-08-30 01:45:13 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9704
2025-08-30 01:45:13 - hyperparameter_tuning - INFO - Trial 2: 发现更好的得分 0.9787
2025-08-30 01:45:14 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-30 01:45:14 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9787
2025-08-30 01:45:14 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-30 01:45:14 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9787
2025-08-30 01:45:14 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-30 01:45:14 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9787
2025-08-30 01:45:14 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-30 01:45:14 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9787
2025-08-30 01:45:14 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-30 01:45:14 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9787
2025-08-30 01:45:14 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-30 01:45:14 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9787
2025-08-30 01:45:14 - hyperparameter_tuning - INFO - 模型 SVM 的最佳参数: {'clf__C': 2.214878027648591, 'clf__kernel': 'linear'}
2025-08-30 01:45:14 - hyperparameter_tuning - INFO - 模型 SVM 的最佳得分: 0.9787
2025-08-30 01:45:14 - hyperparameter_tuning - INFO - 实际执行试验次数: 19/50
2025-08-30 01:45:14 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-30 01:45:14 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\SVM\optimization_history_20250830_014514.html
2025-08-30 01:45:14 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\SVM\param_importances_20250830_014514.html
2025-08-30 01:45:14 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 0.85 秒
2025-08-30 01:45:14 - hyperparameter_tuning - INFO - 开始对 KNN 进行超参数调优，试验次数: 50
2025-08-30 01:45:14 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-30 01:45:14 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-30 01:45:14 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-30 01:45:14 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-30 01:45:14 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 1, 'timeout': 1800}
2025-08-30 01:45:29 - hyperparameter_tuning - WARNING - KNN trial超时，参数: {'clf__n_neighbors': 7, 'clf__weights': 'uniform', 'clf__algorithm': 'auto', 'clf__p': 1}
2025-08-30 01:45:29 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.5000
2025-08-30 01:45:29 - hyperparameter_tuning - INFO - Trial 1: 发现更好的得分 0.9767
2025-08-30 01:45:44 - hyperparameter_tuning - WARNING - KNN trial超时，参数: {'clf__n_neighbors': 5, 'clf__weights': 'distance', 'clf__algorithm': 'auto', 'clf__p': 2}
2025-08-30 01:45:59 - hyperparameter_tuning - WARNING - KNN trial超时，参数: {'clf__n_neighbors': 13, 'clf__weights': 'uniform', 'clf__algorithm': 'auto', 'clf__p': 1}
2025-08-30 01:46:14 - hyperparameter_tuning - WARNING - KNN trial超时，参数: {'clf__n_neighbors': 3, 'clf__weights': 'uniform', 'clf__algorithm': 'auto', 'clf__p': 2}
2025-08-30 01:46:29 - hyperparameter_tuning - WARNING - KNN trial超时，参数: {'clf__n_neighbors': 5, 'clf__weights': 'uniform', 'clf__algorithm': 'auto', 'clf__p': 2}
2025-08-30 01:46:29 - hyperparameter_tuning - INFO - Trial 10: 发现更好的得分 0.9797
2025-08-30 01:46:30 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-30 01:46:30 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9797
2025-08-30 01:46:30 - hyperparameter_tuning - INFO - 模型 KNN 的最佳参数: {'clf__n_neighbors': 15, 'clf__weights': 'distance', 'clf__algorithm': 'kd_tree', 'clf__p': 1}
2025-08-30 01:46:30 - hyperparameter_tuning - INFO - 模型 KNN 的最佳得分: 0.9797
2025-08-30 01:46:30 - hyperparameter_tuning - INFO - 实际执行试验次数: 21/50
2025-08-30 01:46:30 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-30 01:46:30 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\KNN\optimization_history_20250830_014630.html
2025-08-30 01:46:30 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\KNN\param_importances_20250830_014630.html
2025-08-30 01:46:30 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 75.81 秒
2025-08-30 01:46:30 - hyperparameter_tuning - INFO - 开始对 NaiveBayes 进行超参数调优，试验次数: 50
2025-08-30 01:46:30 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 4
2025-08-30 01:46:30 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-30 01:46:30 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-30 01:46:30 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-30 01:46:30 - hyperparameter_tuning - INFO - NaiveBayes模型无需调参，计算基准得分
2025-08-30 01:46:30 - hyperparameter_tuning - INFO - NaiveBayes基准得分: 0.9525
2025-08-30 01:46:30 - hyperparameter_tuning - INFO - 开始对 NeuralNet 进行超参数调优，试验次数: 50
2025-08-30 01:46:30 - hyperparameter_tuning - INFO - 调优策略: TPE, 并行作业数: 6
2025-08-30 01:46:30 - hyperparameter_tuning - INFO - 调参评分指标: roc_auc
2025-08-30 01:46:30 - hyperparameter_tuning - INFO - 超时设置: 1800秒
2025-08-30 01:46:30 - hyperparameter_tuning - INFO - 启用早停机制，耐心值: 10，最小改善: 0.001
2025-08-30 01:46:30 - hyperparameter_tuning - INFO - 开始优化，参数: {'n_trials': 50, 'n_jobs': 6, 'timeout': 1800}
2025-08-30 01:46:37 - hyperparameter_tuning - INFO - Trial 0: 发现更好的得分 0.9667
2025-08-30 01:46:37 - hyperparameter_tuning - INFO - Trial 5: 发现更好的得分 0.9702
2025-08-30 01:46:45 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-30 01:46:45 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9702
2025-08-30 01:46:48 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-30 01:46:48 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9702
2025-08-30 01:46:49 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-30 01:46:49 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9702
2025-08-30 01:46:49 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-30 01:46:49 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9702
2025-08-30 01:46:49 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-30 01:46:49 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9702
2025-08-30 01:46:49 - hyperparameter_tuning - INFO - 早停触发：连续 10 轮没有显著改善（阈值: 0.001）
2025-08-30 01:46:49 - hyperparameter_tuning - INFO - 当前最佳得分: 0.9702
2025-08-30 01:46:49 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳参数: {'clf__hidden_layer_sizes': (50,), 'clf__alpha': 0.0015060857181962678}
2025-08-30 01:46:49 - hyperparameter_tuning - INFO - 模型 NeuralNet 的最佳得分: 0.9702
2025-08-30 01:46:49 - hyperparameter_tuning - INFO - 实际执行试验次数: 17/50
2025-08-30 01:46:49 - hyperparameter_tuning - INFO - 由于早停机制，调优提前结束
2025-08-30 01:46:49 - hyperparameter_tuning - INFO - 优化历史图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\NeuralNet\optimization_history_20250830_014649.html
2025-08-30 01:46:49 - hyperparameter_tuning - INFO - 超参数重要性图已保存为HTML: d:\Code\MM01U\multi_model_02_updated\output\NeuralNet\param_importances_20250830_014649.html
2025-08-30 01:46:49 - hyperparameter_tuning - INFO - 超参数调优完成，耗时: 19.01 秒
