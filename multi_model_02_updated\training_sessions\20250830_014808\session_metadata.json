{"session_id": "20250830_014808", "session_name": "训练_N-2_20250830_014808", "description": "自动创建的训练会话，基于数据文件: N-2", "created_time": "2025-08-30T01:48:08.763732", "last_modified": "2025-08-30T01:48:11.183001", "trained_models": [{"model_name": "DecisionTree", "model_type": "single", "filename": "DecisionTree_single_014808.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250830_014808\\models\\DecisionTree_single_014808.joblib", "save_time": "2025-08-30T01:48:08.904571"}, {"model_name": "RandomForest", "model_type": "single", "filename": "RandomForest_single_014809.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250830_014808\\models\\RandomForest_single_014809.joblib", "save_time": "2025-08-30T01:48:09.145625"}, {"model_name": "XGBoost", "model_type": "single", "filename": "XGBoost_single_014809.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250830_014808\\models\\XGBoost_single_014809.joblib", "save_time": "2025-08-30T01:48:09.404605"}, {"model_name": "LightGBM", "model_type": "single", "filename": "LightGBM_single_014809.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250830_014808\\models\\LightGBM_single_014809.joblib", "save_time": "2025-08-30T01:48:09.570636"}, {"model_name": "CatBoost", "model_type": "single", "filename": "CatBoost_single_014810.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250830_014808\\models\\CatBoost_single_014810.joblib", "save_time": "2025-08-30T01:48:10.397762"}, {"model_name": "Logistic", "model_type": "single", "filename": "Logistic_single_014810.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250830_014808\\models\\Logistic_single_014810.joblib", "save_time": "2025-08-30T01:48:10.536658"}, {"model_name": "SVM", "model_type": "single", "filename": "SVM_single_014810.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250830_014808\\models\\SVM_single_014810.joblib", "save_time": "2025-08-30T01:48:10.661802"}, {"model_name": "KNN", "model_type": "single", "filename": "KNN_single_014810.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250830_014808\\models\\KNN_single_014810.joblib", "save_time": "2025-08-30T01:48:10.796149"}, {"model_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "model_type": "single", "filename": "NaiveBayes_single_014810.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250830_014808\\models\\NaiveBayes_single_014810.joblib", "save_time": "2025-08-30T01:48:10.930964"}, {"model_name": "NeuralNet", "model_type": "single", "filename": "NeuralNet_single_014811.joblib", "filepath": "d:\\Code\\MM01U\\multi_model_02_updated\\training_sessions\\20250830_014808\\models\\NeuralNet_single_014811.joblib", "save_time": "2025-08-30T01:48:11.177045"}], "ensemble_results": [], "data_files": [], "plots": [], "logs": [], "status": "created"}