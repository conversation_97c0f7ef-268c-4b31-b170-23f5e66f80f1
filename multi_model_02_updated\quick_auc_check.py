import numpy as np
from joblib import load
from pathlib import Path
from sklearn.metrics import roc_auc_score

# 设置路径
cache_path = Path(r"D:\Code\MM01U\multi_model_02_updated\cache")

# 模型列表
model_names = ['DecisionTree', 'RandomForest', 'XGBoost', 'LightGBM', 'CatBoost',
               'Logistic', 'SVM', 'NeuralNet', 'NaiveBayes', 'KNN']

print("模型AUC分析结果")
print("=" * 50)

auc_results = []

for model_name in model_names:
    cache_file = cache_path / f"{model_name}_results.joblib"
    
    if cache_file.exists():
        try:
            result = load(cache_file)
            y_true = result['y_true']
            y_pred = result['y_pred']
            y_score = result.get('y_score', None)
            
            # 计算准确率
            accuracy = np.mean(y_true == y_pred)
            
            # 计算AUC
            auc = None
            if y_score is not None:
                if hasattr(y_score, 'ravel'):
                    y_score_flat = y_score.ravel()
                else:
                    y_score_flat = y_score
                    
                if len(np.unique(y_true)) == 2:
                    try:
                        auc = roc_auc_score(y_true, y_score_flat)
                    except:
                        auc = None
            
            # 记录结果
            auc_results.append({
                'model': model_name,
                'accuracy': accuracy,
                'auc': auc,
                'has_y_score': y_score is not None
            })
            
            # 打印结果
            auc_str = f"{auc:.4f}" if auc is not None else "N/A"
            if auc == 1.0:
                auc_str = f"*** {auc_str} ***"
            print(f"{model_name:12s} | 准确率: {accuracy:.4f} | AUC: {auc_str}")
            
        except Exception as e:
            print(f"{model_name:12s} | 错误: {str(e)}")

# 总结
print("\n" + "=" * 50)
print("总结:")
auc_1_count = sum(1 for r in auc_results if r.get('auc') == 1.0)
print(f"AUC = 1.0的模型数量: {auc_1_count}/{len(auc_results)}")

if auc_1_count > 0:
    print("\nAUC=1.0的模型:")
    for r in auc_results:
        if r.get('auc') == 1.0:
            print(f"  - {r['model']}")