@echo off
cd /d "D:\Code\MM01U\multi_model_02_updated"
echo Deleting test files...

del /Q /F check_auc_direct.py 2>nul
del /Q /F analyze_cached_auc.py 2>nul
del /Q /F simple_auc_analysis.py 2>nul
del /Q /F quick_auc_check.py 2>nul
del /Q /F manual_auc_check.py 2>nul
del /Q /F parse_auc_report.py 2>nul
del /Q /F auc_analysis_report.md 2>nul

cd code
del /Q /F check_auc_issue.py 2>nul
del /Q /F analyze_cached_auc.py 2>nul

echo.
echo Checking if files are deleted...
echo.
cd ..

if exist check_auc_direct.py (
    echo check_auc_direct.py - NOT DELETED
) else (
    echo check_auc_direct.py - DELETED
)

if exist analyze_cached_auc.py (
    echo analyze_cached_auc.py - NOT DELETED
) else (
    echo analyze_cached_auc.py - DELETED
)

if exist simple_auc_analysis.py (
    echo simple_auc_analysis.py - NOT DELETED
) else (
    echo simple_auc_analysis.py - DELETED
)

if exist quick_auc_check.py (
    echo quick_auc_check.py - NOT DELETED
) else (
    echo quick_auc_check.py - DELETED
)

if exist manual_auc_check.py (
    echo manual_auc_check.py - NOT DELETED
) else (
    echo manual_auc_check.py - DELETED
)

if exist parse_auc_report.py (
    echo parse_auc_report.py - NOT DELETED
) else (
    echo parse_auc_report.py - DELETED
)

if exist auc_analysis_report.md (
    echo auc_analysis_report.md - NOT DELETED
) else (
    echo auc_analysis_report.md - DELETED
)

echo.
echo Files in code directory:
if exist code\check_auc_issue.py (
    echo code\check_auc_issue.py - NOT DELETED
) else (
    echo code\check_auc_issue.py - DELETED
)

if exist code\analyze_cached_auc.py (
    echo code\analyze_cached_auc.py - NOT DELETED
) else (
    echo code\analyze_cached_auc.py - DELETED
)

pause